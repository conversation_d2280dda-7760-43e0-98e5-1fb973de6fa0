import PageWrapper from '@/components/hybrid-page-wrap';
import { useJsbridgeReady, useLocale, useUserInfo } from '@/hooks';
import { getQueryParams } from '@/modules/utils';
import { useEffect, useState } from 'react';
import locale from './locale';

export default function UserLevel() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();
  const isReady = useJsbridgeReady();

  // 模拟用户等级数据
  const [userLevel] = useState({
    currentLevel: 33,
    nextLevel: 34,
    progress: 12, // 进度 0-100
    current: 0,
    total: 5,
    avatar: 'https://via.placeholder.com/80x80' // 占位符头像
  });

  useEffect(() => {
    /* if (userInfo && isReady) {
      report('popup_page_show', { type: '', ...queryParams, uid: userInfo.uid });
    } */
  }, [userInfo, isReady]);

  const upgradeItems = [
    {
      icon: '🎥',
      bgColor: 'from-blue-500 to-blue-600',
      title: t('videoChat'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '💬',
      bgColor: 'from-green-500 to-green-600',
      title: t('replyMessage'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '🎁',
      bgColor: 'from-purple-500 to-purple-600',
      title: t('receiveGifts'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '🖼️',
      bgColor: 'from-pink-500 to-pink-600',
      title: t('unlockPictures'),
      description: t('expDescription', { points: 300, exp: 1 })
    }
  ];

  return (
    <PageWrapper title={t('title')}>
      <div className="min-h-screen bg-gradient-to-b from-amber-900 via-amber-800 to-amber-900 text-white">
        {/* 用户等级进度区域 */}
        <div className="px-40px pb-40px pt-60px relative">
          {/* 进度圆环 */}
          <div className="h-320px w-320px relative mx-auto flex items-center justify-center">
            {/* 外圆环 - 半透明白色的圆弧 */}
            <div
              className="border-10px absolute inset-0 rounded-full border-white/20"
              style={{
                clipPath: 'path("M 160 160 m -150, 0 a 150,150 0 1,1 300,0")'
              }}
            ></div>

            {/* 进度圆环 - 有颜色的元素 */}
            <div
              className="border-10px absolute inset-0 rounded-full border-orange-400"
              style={{
                clipPath: 'path("M 160 160 m -150, 0 a 150,150 0 1,1 300,0")',
                background: `conic-gradient(from 180deg, #ff8c42 ${userLevel.progress * 1.8}deg, transparent 0deg)`
              }}
            ></div>

            {/* 用户头像 */}
            <div className="relative z-10">
              <div
                className="h-100px w-100px border-3px rounded-full border-white bg-cover bg-center shadow-lg"
                style={{ backgroundImage: `url(${userLevel.avatar})` }}
              ></div>

              {/* 等级标签 */}
              <div className="-bottom-12px absolute left-1/2 -translate-x-1/2 transform">
                <div className="px-16px py-6px text-20px rounded-full bg-gradient-to-r from-yellow-400 to-orange-400 font-bold text-black shadow-md">
                  {t('currentLevel', { level: userLevel.currentLevel })}
                </div>
              </div>
            </div>

            {/* 进度百分比 */}
            <div
              className="absolute left-0 top-1/2 -translate-y-1/2 transform"
              style={{
                transform: `rotate(${userLevel.progress * 1.8}deg) translate(160px) rotate(-${userLevel.progress * 1.8}deg)`
              }}
            >
              <div className="px-12px py-4px text-18px rounded-full bg-white font-bold text-orange-500 shadow-sm">
                {userLevel.progress}%
              </div>
            </div>

            {/* 进度数值 */}
            <div className="bottom-40px left-40px text-18px absolute text-white/80">
              {t('progress', { current: userLevel.current, total: userLevel.total })}
            </div>

            {/* 下一级别 */}
            <div className="bottom-40px right-40px text-18px absolute text-white/80">
              {t('nextLevel', { level: userLevel.nextLevel })}
            </div>
          </div>
        </div>

        {/* 等级奖励卡片 */}
        <div className="mb-40px px-40px">
          <div className="rounded-24px p-32px border border-white/15 bg-black/40 shadow-lg backdrop-blur-sm">
            <div className="text-center">
              <div className="mb-24px gap-12px flex items-center justify-center">
                <span className="text-32px text-yellow-400">⭐</span>
                <h3 className="text-28px font-bold">{t('levelRewards', { level: 1 })}</h3>
                <span className="text-32px text-yellow-400">⭐</span>
              </div>

              {/* 礼品盒图标区域 */}
              <div className="mb-32px relative">
                <div className="h-160px w-160px rounded-20px relative mx-auto flex items-center justify-center overflow-hidden bg-gradient-to-br from-red-500 via-red-600 to-red-700 shadow-xl">
                  {/* 礼品盒主体 */}
                  <div className="text-80px relative z-10">🎁</div>
                  {/* 光效背景 */}
                  <div className="absolute inset-0 bg-gradient-to-tr from-yellow-400/20 to-transparent"></div>
                </div>
              </div>

              <button className="px-40px py-16px text-28px w-full rounded-full bg-gradient-to-r from-orange-500 to-yellow-500 font-bold text-black shadow-lg transition-shadow hover:shadow-xl">
                {t('claim')}
              </button>
            </div>
          </div>
        </div>

        {/* 升级方式 */}
        <div className="px-40px">
          <div className="mb-24px gap-12px flex items-center">
            <div className="h-32px w-6px rounded bg-orange-500"></div>
            <h3 className="text-28px font-bold">{t('howToUpgrade')}</h3>
          </div>

          <div className="space-y-16px">
            {upgradeItems.map((item, index) => (
              <div
                key={index}
                className="gap-24px rounded-16px p-24px flex items-center border border-white/15 bg-black/25 shadow-sm backdrop-blur-sm"
              >
                <div
                  className={`h-64px w-64px rounded-16px text-32px flex items-center justify-center bg-gradient-to-br ${item.bgColor} shadow-md`}
                >
                  {item.icon}
                </div>
                <div className="flex-1">
                  <h4 className="mb-4px text-24px font-bold text-white">{item.title}</h4>
                  <p className="text-18px text-white/75">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="h-40px"></div>
      </div>
    </PageWrapper>
  );
}
