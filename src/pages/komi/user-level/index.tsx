import PageWrapper from '@/components/hybrid-page-wrap';
import { useJsbridgeReady, useLocale, useUserInfo } from '@/hooks';
import { getQueryParams } from '@/modules/utils';
import { useEffect, useState } from 'react';
import locale from './locale';

// 半圆弧形进度条组件
interface SemiCircleProgressProps {
  progress: number; // 0-100的进度值
  size?: number; // 进度条的大小
  strokeWidth?: number; // 进度条的宽度
}

function SemiCircleProgress({ progress, size = 200, strokeWidth = 12 }: SemiCircleProgressProps) {
  // 确保进度值在0-100之间
  const normalizedProgress = Math.max(0, Math.min(100, progress));

  // 计算半圆的参数
  const radius = (size - strokeWidth) / 2;
  const centerX = size / 2;
  const centerY = size / 2;

  // 创建完整半圆弧的路径（从左到右，180度）
  const backgroundPath = `
    M ${centerX - radius} ${centerY}
    A ${radius} ${radius} 0 0 1 ${centerX + radius} ${centerY}
  `;

  // 计算进度弧的结束位置
  // 进度从左侧（180度）开始，向右侧（0度）绘制
  // 在SVG坐标系中，需要使用负的sin值来保持在上半圆
  const progressAngle = Math.PI - (normalizedProgress / 100) * Math.PI;
  const endX = centerX + radius * Math.cos(progressAngle);
  const endY = centerY - radius * Math.sin(progressAngle);

  // 创建进度弧的路径
  const progressPath =
    normalizedProgress > 0
      ? `
    M ${centerX - radius} ${centerY}
    A ${radius} ${radius} 0 ${normalizedProgress > 50 ? 1 : 0} 1 ${endX} ${endY}
  `
      : '';

  return (
    <div className="flex flex-col items-center">
      <svg width={size} height={size / 2 + strokeWidth / 2} className="overflow-visible">
        {/* 背景半圆弧 */}
        <path d={backgroundPath} fill="none" stroke="#f0f0f0" strokeWidth={strokeWidth} strokeLinecap="round" />
        {/* 进度半圆弧 */}
        {normalizedProgress > 0 && (
          <path d={progressPath} fill="none" stroke="#ff8c00" strokeWidth={strokeWidth} strokeLinecap="round" />
        )}
      </svg>
      <div className="mt-4px text-center">
        <div className="text-24px text-#333 font-bold">{normalizedProgress}%</div>
        <div className="text-14px text-#999 mt-2px">当前进度</div>
      </div>
    </div>
  );
}

export default function UserLevel() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();
  const isReady = useJsbridgeReady();

  // 模拟用户等级数据
  const [userLevel] = useState({
    currentProgress: 50
  });

  useEffect(() => {
    /* if (userInfo && isReady) {
      report('popup_page_show', { type: '', ...queryParams, uid: userInfo.uid });
    } */
  }, [userInfo, isReady]);

  return (
    <PageWrapper title={t('title')}>
      <div className="page p-20px">
        <div className="flex justify-center">
          <SemiCircleProgress progress={userLevel.currentProgress} size={300} strokeWidth={16} />
        </div>
      </div>
    </PageWrapper>
  );
}
