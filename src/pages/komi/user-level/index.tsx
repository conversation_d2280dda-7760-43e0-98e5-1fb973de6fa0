import PageWrapper from '@/components/hybrid-page-wrap';
import { useJsbridgeReady, useLocale, useUserInfo } from '@/hooks';
import { getQueryParams } from '@/modules/utils';
import { useEffect, useState } from 'react';
import locale from './locale';

export default function UserLevel() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();
  const isReady = useJsbridgeReady();

  // 模拟用户等级数据
  const [userLevel] = useState({
    currentLevel: 33,
    nextLevel: 34,
    progress: 12, // 进度 0-100
    current: 0,
    total: 5,
    avatar: 'https://via.placeholder.com/80x80' // 占位符头像
  });

  useEffect(() => {
    /* if (userInfo && isReady) {
      report('popup_page_show', { type: '', ...queryParams, uid: userInfo.uid });
    } */
  }, [userInfo, isReady]);

  const upgradeItems = [
    {
      icon: 'https://static.airpackapp.com/komi/user-level/<EMAIL>',
      title: t('videoChat'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: 'https://static.airpackapp.com/komi/user-level/reply-message.png',
      title: t('replyMessage'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: 'https://static.airpackapp.com/komi/user-level/receive-gifts.png',
      title: t('receiveGifts'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: 'https://static.airpackapp.com/komi/user-level/unlock-picture.png',
      title: t('unlockPictures'),
      description: t('expDescription', { points: 300, exp: 1 })
    }
  ];

  return (
    <PageWrapper title={t('title')}>
      <div
        className="min-h-screen text-white bg-center bg-no-repeat bg-cover"
        style={{ backgroundImage: 'url(https://static.airpackapp.com/komi/user-level/bg.png)' }}
      >
        {/* 用户等级进度区域 */}
        <div className="relative flex h-[250px] w-full items-center justify-center">
          <div className="relative h-[210px] w-[320px]">
            {/* Conic Gradient for Progress */}
            <div
              className="absolute left-0 top-0 h-[320px] w-[320px] rounded-full"
              style={{
                top: '-105px',
                background: `conic-gradient(from -60deg at 50% 100%, #F97316 ${userLevel.progress * 2.1}deg, #4B5563 ${userLevel.progress * 2.1}deg 210deg, transparent 210deg)`
              }}
            ></div>

            {/* Mask for the inner hole */}
            <div
              className="absolute h-[296px] w-[296px] rounded-full"
              style={{
                top: '-93px',
                left: '12px',
                backgroundImage: 'inherit',
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            ></div>

            {/* Mask for the bottom part to create the arc */}
            <div
              className="absolute bottom-0 h-[105px] w-full"
              style={{
                backgroundImage: 'inherit',
                backgroundSize: 'cover',
                backgroundPosition: 'center bottom'
              }}
            ></div>

            {/* Avatar & Level Badge */}
            <div className="absolute z-10 flex h-full w-full flex-col items-center justify-start pt-[25px]">
              <div
                className="h-[80px] w-[80px] rounded-full border-[3px] border-white bg-cover bg-center shadow-lg"
                style={{ backgroundImage: `url(${userLevel.avatar})` }}
              ></div>
              <div className="relative h-[28px] w-[66px] translate-y-[-14px]">
                <img
                  src="https://static.airpackapp.com/komi/user-level/pj_zs_.png"
                  alt="level badge"
                  className="h-full w-full"
                />
                <div className="absolute inset-0 flex items-center justify-center text-[14px] font-bold text-white">
                  {t('currentLevel', { level: userLevel.currentLevel })}
                </div>
              </div>
            </div>

            {/* Progress Dot & Percentage */}
            <div
              className="absolute left-1/2 top-[105px] h-0 w-0"
              style={{
                transform: `rotate(${-105 + (userLevel.progress / 100) * 210}deg)`
              }}
            >
              <div className="flex -translate-y-[148px] flex-col items-center">
                <div className="rounded-full bg-white px-[8px] py-[2px] text-[12px] font-bold text-orange-500 shadow-sm">
                  {userLevel.progress.toFixed(1)}%
                </div>
                <div className="mt-[4px] h-[12px] w-[12px] rounded-full bg-white shadow-lg"></div>
              </div>
            </div>

            {/* Progress Text */}
            <div className="absolute bottom-[10px] left-[20px] text-[14px] text-white/80">
              {t('progress', { current: userLevel.current, total: userLevel.total })}
            </div>
            <div className="absolute bottom-[10px] right-[20px] text-[14px] text-white/80">
              {t('nextLevel', { level: userLevel.nextLevel })}
            </div>
          </div>
        </div>

        {/* 等级奖励卡片 */}
        <div className="mb-40px px-40px mt-[-40px]">
          <div className="rounded-24px p-32px bg-black/20 shadow-lg backdrop-blur-sm relative">
            <img src="https://static.airpackapp.com/komi/user-level/starts.png" alt="starts" className="absolute top-10px left-10px h-48px" />
            <img src="https://static.airpackapp.com/komi/user-level/starts.png" alt="starts" className="absolute top-10px right-10px h-48px" />
            <div className="text-center">
              <div className="mb-24px gap-12px flex items-center justify-center">
                <h3 className="text-28px font-bold">{t('levelRewards', { level: 1 })}</h3>
              </div>

              {/* 礼品盒图标区域 */}
              <div className="mb-32px relative">
                <img src="https://static.airpackapp.com/komi/user-level/gift.png" alt="gift" className="h-160px mx-auto" />
              </div>

              <button className="px-40px py-16px text-28px w-full rounded-full bg-gradient-to-r from-orange-500 to-yellow-500 font-bold text-black shadow-lg transition-shadow hover:shadow-xl">
                {t('claim')}
              </button>
            </div>
          </div>
        </div>

        {/* 升级方式 */}
        <div className="px-40px">
          <div className="mb-24px gap-12px flex items-center">
            <div className="h-32px w-6px rounded bg-orange-500"></div>
            <h3 className="text-28px font-bold">{t('howToUpgrade')}</h3>
          </div>

          <div className="space-y-24px">
            {upgradeItems.map((item, index) => (
              <div
                key={index}
                className="gap-24px rounded-16px p-24px flex items-center bg-black/10 shadow-sm backdrop-blur-sm"
              >
                <div
                  className={'h-64px w-64px rounded-16px text-32px flex items-center justify-center shadow-md'}
                >
                  <img src={item.icon} alt="icon" className="h-full" />
                </div>
                <div className="flex-1">
                  <h4 className="mb-4px text-24px font-bold text-white">{item.title}</h4>
                  <p className="text-18px text-white/75">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="h-40px"></div>
      </div>
    </PageWrapper>
  );
}
